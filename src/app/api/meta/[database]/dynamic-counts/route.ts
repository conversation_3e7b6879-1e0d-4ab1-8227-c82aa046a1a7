import { NextResponse } from 'next/server';
import { getDynamicModel, validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { buildDrizzleWhere } from '@/lib/server/buildDrizzleWhere';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    const database = rawDatabase.toLowerCase();

    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    const config = await getDatabaseConfig(database);
    if (!config) {
      return NextResponse.json(
        { success: false, error: '未找到数据库配置' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const filtersParam = searchParams.get('filters');
    const fieldName = searchParams.get('field');

    if (!fieldName) {
      return NextResponse.json(
        { success: false, error: '缺少字段名参数' },
        { status: 400 }
      );
    }

    const model = await getDynamicTable(database);

    // 构建完整的搜索参数，包括文本搜索和筛选条件
    const fullSearchParams = new URLSearchParams();

    if (filtersParam) {
      try {
        const parsedFilters = JSON.parse(filtersParam);

        Object.entries(parsedFilters).forEach(([key, value]) => {
          if (key !== fieldName && value !== null && value !== undefined && value !== '') {
            if (Array.isArray(value)) {
              // 对于数组值，需要特殊处理
              value.forEach(v => fullSearchParams.append(key, String(v)));
            } else {
              fullSearchParams.set(key, String(value));
            }
          }
        });
      } catch (_e) {
        console.error("解析筛选条件失败:", _e);
      }
    }

    // 使用统一的where条件构建函数，确保与主搜索逻辑一致
    const baseWhere = buildDrizzleWhere(fullSearchParams, config);

    // 获取该字段的所有唯一值及其计数
    const fieldConfig = config.fields.find(f => f.fieldName === fieldName);
    if (!fieldConfig) {
      return NextResponse.json(
        { success: false, error: '字段配置未找到' },
        { status: 404 }
      );
    }

    try {
      // 分别获取非空值和空值的计数
      // 1. 获取非空值的计数
      const groupByResult = await (model as any).groupBy({
        by: [fieldName],
        where: {
          ...baseWhere,
          [fieldName]: { not: null, notIn: [''] } // 排除null和空字符串
        },
        _count: { [fieldName]: true },
        orderBy: { _count: { [fieldName]: 'desc' } }
      });

      // 2. 单独统计空值（null和空字符串）
      const nullCount = await (model as any).count({
        where: {
          ...baseWhere,
          OR: [
            { [fieldName]: null },
            { [fieldName]: '' }
          ]
        }
      });

      // 3. 合并非空值和空值的结果
      const nonNullCounts = groupByResult.map((item: Record<string, unknown>) => ({
        value: String(item[fieldName]).trim(),
        count: (item._count as any)[fieldName],
        isNull: false
      }));

      // 4. 添加N/A项（如果有空值）
      const counts = [...nonNullCounts];
      if (nullCount > 0) {
        counts.push({
          value: 'N/A',
          count: nullCount,
          isNull: true
        });
      }

      // 5. 排序：按计数降序，N/A项排在最后
      counts.sort((a: Record<string, unknown>, b: Record<string, unknown>) => {
        if (a.isNull && !b.isNull) return 1;
        if (!a.isNull && b.isNull) return -1;
        return (b.count as number) - (a.count as number); // 按计数降序排列
      });

      // 6. 移除内部使用的isNull字段
      const finalCounts = counts.map((item: Record<string, unknown>) => ({
        value: item.value,
        count: item.count
      }));

      return NextResponse.json({
        success: true,
        data: finalCounts,
        field: fieldName
      });
    } catch (__error) {
      console.error(`Failed to fetch dynamic counts:`, __error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch dynamic counts' },
        { status: 500 }
      );
    }
  } catch (__error) {
    console.error(`Dynamic counts API error:`, __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 