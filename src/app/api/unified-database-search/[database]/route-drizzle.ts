import { type NextRequest, NextResponse } from 'next/server';
import { DrizzleUnifiedSearchService } from '@/lib/services/drizzleUnifiedSearchService';
import { validateDatabaseCode } from '@/lib/drizzleTableMapping';

export const dynamic = 'force-dynamic';

/**
 * GET /api/unified-database-search/[database]?q=<keyword>&page=1&limit=20&filters={}
 * 新版数据库页内搜索API - Drizzle版本
 * 使用ES搜索 -> Drizzle回捞 -> 二次筛选排序分页
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const searchParams = request.nextUrl.searchParams;
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error
      }, { status: validation.status || 400 });
    }
    
    // 解析查询参数
    const query = searchParams.get('q') || '';
    const allFields = searchParams.get('allFields') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20', 10), 100);
    const sortBy = searchParams.get('sortBy');
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';
    
    // 解析筛选条件
    const filters: Record<string, unknown> = {};
    searchParams.forEach((value, key) => {
      if (!['q', 'allFields', 'page', 'limit', 'sortBy', 'sortOrder'].includes(key)) {
        const allValues = searchParams.getAll(key);
        filters[key] = allValues.length === 1 ? allValues[0] : allValues;
      }
    });
    
    console.log('[API] Drizzle数据库搜索GET请求:', { 
      database, 
      query: query || allFields, 
      page, 
      limit, 
      sortBy, 
      sortOrder,
      filtersCount: Object.keys(filters).length
    });
    
    // 使用Drizzle统一搜索服务
    const result = await DrizzleUnifiedSearchService.unifiedSearch({
      query: query || allFields || '',
      table_code: database,
      filters,
      sort_by: sortBy,
      sort_order: sortOrder,
      page,
      limit
    });
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    const targetResult = result.data.results.find(r => r.table_code === database);
    if (!targetResult) {
      return NextResponse.json({
        success: true,
        data: [],
        pagination: result.data.pagination,
        search_info: result.data.search_info
      });
    }
    
    return NextResponse.json({
      success: true,
      data: targetResult.data,
      pagination: {
        ...result.data.pagination,
        total_results: targetResult.total
      },
      search_info: {
        ...result.data.search_info,
        es_total: targetResult.es_total,
        drizzle_total: targetResult.total
      }
    });
    
  } catch (error) {
    console.error('[API] Drizzle数据库搜索GET失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索失败'
    }, { status: 500 });
  }
}

/**
 * POST /api/unified-database-search/[database]
 * 支持更复杂的数据库搜索参数 - Drizzle版本
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error
      }, { status: validation.status || 400 });
    }
    
    const body = await request.json();
    const { 
      query = '', 
      filters = {}, 
      sort_by, 
      sort_order = 'desc',
      page = 1, 
      limit = 20 
    } = body;
    
    console.log('[API] Drizzle数据库搜索POST请求:', { 
      database, 
      query, 
      page, 
      limit, 
      sort_by, 
      sort_order,
      filters 
    });
    
    // 使用Drizzle统一搜索服务
    const result = await DrizzleUnifiedSearchService.unifiedSearch({
      query,
      table_code: database,
      filters,
      sort_by,
      sort_order,
      page,
      limit
    });
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    const targetResult = result.data.results.find(r => r.table_code === database);
    if (!targetResult) {
      return NextResponse.json({
        success: true,
        data: [],
        pagination: result.data.pagination,
        search_info: result.data.search_info
      });
    }
    
    return NextResponse.json({
      success: true,
      data: targetResult.data,
      pagination: {
        ...result.data.pagination,
        total_results: targetResult.total
      },
      search_info: {
        ...result.data.search_info,
        es_total: targetResult.es_total,
        drizzle_total: targetResult.total
      }
    });
    
  } catch (error) {
    console.error('[API] Drizzle数据库搜索POST失败:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索失败'
    }, { status: 500 });
  }
}
