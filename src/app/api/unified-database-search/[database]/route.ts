import { type NextRequest, NextResponse } from 'next/server';
import { DrizzleUnifiedSearchService } from '@/lib/services/drizzleUnifiedSearchService';
import { validateDatabaseCode } from '@/lib/drizzleTableMapping';

export const dynamic = 'force-dynamic';

/**
 * GET /api/unified-database-search/[database]?q=<keyword>&page=1&limit=20&filters={}
 * 新版数据库页内搜索API
 * 使用ES搜索 -> Prisma回捞 -> 二次筛选排序分页
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const searchParams = request.nextUrl.searchParams;
    
    // 验证数据库代码
    if (!validateDatabaseCode(database)) {
      return NextResponse.json({
        success: false,
        error: `不支持的数据库代码: ${database}`
      }, { status: 400 });
    }
    
    // 解析查询参数
    const query = searchParams.get('q')?.trim() || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sort_by = searchParams.get('sortBy') || undefined;
    const sort_order = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';
    
    // 解析筛选条件
    let filters = {};
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        filters = JSON.parse(filtersParam);
      } catch (error) {
        console.warn('[API] 筛选条件解析失败:', error);
      }
    }
    
    // 处理allFields参数（兼容现有逻辑）
    const allFields = searchParams.get('allFields');
    if (allFields) {
      filters = { ...filters, allFields };
    }
    
    console.log('[API] 数据库搜索请求:', { 
      database, 
      query: query || allFields, 
      page, 
      limit, 
      sort_by, 
      sort_order,
      filters 
    });
    
    // 使用统一搜索服务
    const result = await DrizzleUnifiedSearchService.unifiedSearch({
      query: query || allFields || '',
      table_code: database,
      filters,
      sort_by,
      sort_order,
      page,
      limit
    });
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    const targetResult = result.data.results.find(r => r.table_code === database);
    if (!targetResult) {
      return NextResponse.json({
        success: true,
        data: [],
        pagination: result.data.pagination,
        search_info: result.data.search_info
      });
    }
    
    return NextResponse.json({
      success: true,
      data: targetResult.data,
      pagination: {
        ...result.data.pagination,
        total_results: targetResult.total
      },
      search_info: {
        ...result.data.search_info,
        es_total: targetResult.es_total,
        prisma_total: targetResult.total
      }
    });
    
  } catch (error) {
    console.error('[API] 数据库搜索失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索服务异常'
    }, { status: 500 });
  }
}

/**
 * POST /api/unified-database-search/[database]
 * 支持更复杂的数据库搜索参数
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 验证数据库代码
    if (!validateDatabaseCode(database)) {
      return NextResponse.json({
        success: false,
        error: `不支持的数据库代码: ${database}`
      }, { status: 400 });
    }
    
    const body = await request.json();
    const { 
      query = '', 
      filters = {}, 
      sort_by, 
      sort_order = 'desc',
      page = 1, 
      limit = 20 
    } = body;
    
    console.log('[API] 数据库搜索POST请求:', { 
      database, 
      query, 
      page, 
      limit, 
      sort_by, 
      sort_order,
      filters 
    });
    
    // 使用统一搜索服务
    const result = await DrizzleUnifiedSearchService.unifiedSearch({
      query,
      table_code: database,
      filters,
      sort_by,
      sort_order,
      page,
      limit
    });
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    const targetResult = result.data.results.find(r => r.table_code === database);
    if (!targetResult) {
      return NextResponse.json({
        success: true,
        data: [],
        pagination: result.data.pagination,
        search_info: result.data.search_info
      });
    }
    
    return NextResponse.json({
      success: true,
      data: targetResult.data,
      pagination: {
        ...result.data.pagination,
        total_results: targetResult.total
      },
      search_info: {
        ...result.data.search_info,
        es_total: targetResult.es_total,
        prisma_total: targetResult.total
      }
    });
    
  } catch (error) {
    console.error('[API] 数据库搜索POST失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索服务异常'
    }, { status: 500 });
  }
}
